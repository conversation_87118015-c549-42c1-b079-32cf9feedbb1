import React from 'react';
import Layout from '@/components/Layout';
import ToolsPageClient from '@/components/tools/ToolsPageClient';
import { apiClient } from '@/lib/api';

// 服务端数据获取函数
async function getToolsData() {
  try {
    const response = await apiClient.getTools({
      status: 'published',
      limit: 100
    });

    if (response.success && response.data) {
      return {
        tools: response.data.tools,
        error: null
      };
    } else {
      return {
        tools: [],
        error: response.error || '获取工具列表失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch tools:', error);
    return {
      tools: [],
      error: '获取工具列表失败，请稍后重试'
    };
  }
}

export default async function ToolsPage() {
  const { tools, error } = await getToolsData();

  return (
    <Layout>
      <ToolsPageClient initialTools={tools} error={error} />
    </Layout>
  );
}

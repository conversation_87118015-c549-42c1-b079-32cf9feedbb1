import React from 'react';
import Layout from '@/components/Layout';
import ToolsPageClient from '@/components/tools/ToolsPageClient';

// 服务端数据获取函数
async function getToolsData() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

    const response = await fetch(`${baseUrl}/tools?status=published&limit=100`, {
      // 确保每次都获取最新数据
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.data) {
      return {
        tools: data.data.tools,
        error: null
      };
    } else {
      return {
        tools: [],
        error: data.error || '获取工具列表失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch tools:', error);
    return {
      tools: [],
      error: '获取工具列表失败，请稍后重试'
    };
  }
}

export default async function ToolsPage() {
  const { tools, error } = await getToolsData();

  return (
    <Layout>
      <ToolsPageClient initialTools={tools} error={error} />
    </Layout>
  );
}

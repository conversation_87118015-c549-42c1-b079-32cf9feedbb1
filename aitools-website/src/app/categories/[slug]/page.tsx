import React from 'react';
import Layout from '@/components/Layout';
import CategoryPageClient from '@/components/categories/CategoryPageClient';
import { Tool } from '@/lib/api';
import { getCategoryConfig } from '@/constants/categories';

// 分类信息接口
interface CategoryInfo {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

interface CategoryPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// 根据slug生成分类信息的辅助函数
const getCategoryInfoBySlug = (slug: string, toolCount: number): CategoryInfo => {
  const categoryConfig = getCategoryConfig(slug);

  if (categoryConfig) {
    return {
      _id: slug,
      slug,
      name: categoryConfig.name,
      description: categoryConfig.description,
      icon: categoryConfig.icon,
      color: categoryConfig.color,
      toolCount
    };
  }

  // 如果没有找到配置，使用默认值
  return {
    _id: slug,
    slug,
    name: slug.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    description: `AI tools in the ${slug} category.`,
    icon: '🤖',
    color: '#6B7280',
    toolCount
  };
};

// 服务端数据获取函数
async function getCategoryData(slug: string) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

    // 获取该分类下的工具
    const response = await fetch(`${baseUrl}/tools?category=${slug}&status=published&limit=100`, {
      // 确保每次都获取最新数据
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.data) {
      const tools = data.data.tools;
      // 根据分类slug生成分类信息
      const categoryInfo = getCategoryInfoBySlug(slug, tools.length);

      return {
        categoryInfo,
        tools,
        error: null
      };
    } else {
      return {
        categoryInfo: null,
        tools: [],
        error: data.error || '获取分类数据失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch category data:', error);
    return {
      categoryInfo: null,
      tools: [],
      error: '获取分类数据失败，请稍后重试'
    };
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const resolvedParams = await params;
  const { categoryInfo, tools, error } = await getCategoryData(resolvedParams.slug);

  return (
    <Layout>
      <CategoryPageClient
        categoryInfo={categoryInfo}
        tools={tools}
        error={error}
      />
    </Layout>
  );
}

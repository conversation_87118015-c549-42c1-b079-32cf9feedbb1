import React from 'react';
import Layout from '@/components/Layout';
import CategoriesPageClient from '@/components/categories/CategoriesPageClient';
import { CATEGORY_METADATA } from '@/constants/categories';

// 分类页面组件

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

// 服务端数据获取函数
async function getCategoriesData() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

    const response = await fetch(`${baseUrl}/categories`, {
      // 确保每次都获取最新数据
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.data) {
      // 转换API数据格式为组件期望的格式
      const transformedCategories = data.data.categories.map((apiCategory: any) => {
        // 使用统一的分类元数据映射
        const metadata = CATEGORY_METADATA[apiCategory.id] || {
          description: '优质AI工具集合',
          icon: '🔧',
          color: '#6B7280'
        };

        return {
          _id: apiCategory.id,
          name: apiCategory.name,
          slug: apiCategory.id,
          description: metadata.description,
          icon: metadata.icon,
          color: metadata.color,
          toolCount: apiCategory.count
        };
      });

      return {
        categories: transformedCategories,
        error: null
      };
    } else {
      return {
        categories: [],
        error: data.error || '获取分类列表失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    return {
      categories: [],
      error: '获取分类列表失败，请稍后重试'
    };
  }
}

export default async function CategoriesPage() {
  const { categories, error } = await getCategoriesData();

  return (
    <Layout>
      <CategoriesPageClient categories={categories} error={error} />
    </Layout>
  );
}

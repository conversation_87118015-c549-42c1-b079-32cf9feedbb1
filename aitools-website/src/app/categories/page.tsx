import React from 'react';
import Layout from '@/components/Layout';
import CategoriesPageClient from '@/components/categories/CategoriesPageClient';
import { CATEGORY_METADATA } from '@/constants/categories';
import { apiClient } from '@/lib/api';

// 分类页面组件

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

// 服务端数据获取函数
async function getCategoriesData() {
  try {
    const response = await apiClient.getCategories();

    if (response.success && response.data) {
      // 转换API数据格式为组件期望的格式
      const transformedCategories = response.data.categories.map((apiCategory: any) => {
        // 使用统一的分类元数据映射
        const metadata = CATEGORY_METADATA[apiCategory.id] || {
          description: '优质AI工具集合',
          icon: '🔧',
          color: '#6B7280'
        };

        return {
          _id: apiCategory.id,
          name: apiCategory.name,
          slug: apiCategory.id,
          description: metadata.description,
          icon: metadata.icon,
          color: metadata.color,
          toolCount: apiCategory.count
        };
      });

      return {
        categories: transformedCategories,
        error: null
      };
    } else {
      return {
        categories: [],
        error: response.error || '获取分类列表失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    return {
      categories: [],
      error: '获取分类列表失败，请稍后重试'
    };
  }
}

export default async function CategoriesPage() {
  const { categories, error } = await getCategoriesData();

  return (
    <Layout>
      <CategoriesPageClient categories={categories} error={error} />
    </Layout>
  );
}

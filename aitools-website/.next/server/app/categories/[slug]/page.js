(()=>{var e={};e.id=5093,e.ids=[5093],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(93613),l=t(11860);function i({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15730:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c={children:["",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,50203)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/categories/[slug]/page",pathname:"/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21960:(e,s,t)=>{Promise.resolve().then(t.bind(t,94253))},25366:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},50203:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx","default")},58408:(e,s,t)=>{Promise.resolve().then(t.bind(t,50203))},62185:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let r=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{constructor(e=r){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}}let l=new a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71123:(e,s,t)=>{"use strict";t.d(s,{Bi:()=>i,PZ:()=>o,ch:()=>a,vK:()=>n,xO:()=>l});let r=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],a=r.map(e=>({value:e.slug,label:e.name})),l=[{value:"",label:"所有分类"},...a],i=r.reduce((e,s)=>(e[s.slug]=s.name,e),{}),n=r.reduce((e,s)=>(e[s.slug]=s,e),{}),o=e=>n[e];r.map(e=>e.slug)},78272:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94253:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(60687),a=t(43210),l=t(85814),i=t.n(l),n=t(16189),o=t(98402),c=t(73899),d=t(33823),x=t(11011);t(62185),t(71123);var m=t(28559),h=t(80462),u=t(78272),p=t(6943),g=t(25366);let b=[{value:"",label:"所有价格"},{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}],j=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function y(){(0,n.useParams)();let[e,s]=(0,a.useState)(null),[t,l]=(0,a.useState)([]),[y,v]=(0,a.useState)(!0),[f,N]=(0,a.useState)(""),[w,A]=(0,a.useState)(""),[k,C]=(0,a.useState)(""),[P,q]=(0,a.useState)("popular"),[S,$]=(0,a.useState)("grid"),[T,_]=(0,a.useState)(!1),E=[...t.filter(e=>{let s=e.name.toLowerCase().includes(w.toLowerCase())||e.description.toLowerCase().includes(w.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(w.toLowerCase())),t=!k||e.pricing===k;return s&&t})].sort((e,s)=>{switch(P){case"popular":return(s.likes||0)-(e.likes||0);case"views":return(s.views||0)-(e.views||0);case"name":return e.name.localeCompare(s.name);case"newest":if(e.createdAt&&s.createdAt)return new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime();return 0;default:return 0}});return y?(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(d.A,{size:"lg"})})}):f?(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(x.A,{message:f})})}):e?(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,r.jsx)(i(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)(i(),{href:"/categories",className:"hover:text-blue-600",children:"分类"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900",children:e.name})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(i(),{href:"/categories",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"返回分类列表"]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-3xl",style:{backgroundColor:e.color},children:(0,r.jsx)("span",{className:"text-white",children:e.icon})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:e.name}),(0,r.jsxs)("p",{className:"text-lg text-gray-600",children:[e.toolCount," 个工具"]})]})]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)("input",{type:"text",placeholder:"在此分类中搜索工具...",value:w,onChange:e=>A(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(h.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,r.jsx)("div",{className:"md:hidden mb-4",children:(0,r.jsxs)("button",{onClick:()=>_(!T),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,r.jsx)(u.A,{className:`ml-2 h-4 w-4 transform ${T?"rotate-180":""}`})]})}),(0,r.jsxs)("div",{className:`grid grid-cols-1 md:grid-cols-3 gap-4 ${T?"block":"hidden md:grid"}`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,r.jsx)("select",{value:k,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:b.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,r.jsx)("select",{value:P,onChange:e=>q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:j.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,r.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,r.jsx)("button",{onClick:()=>$("grid"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${"grid"===S?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(p.A,{className:"h-4 w-4 mx-auto"})}),(0,r.jsx)("button",{onClick:()=>$("list"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${"list"===S?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(g.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["显示 ",E.length," 个结果",w&&` 搜索 "${w}"`]})}),E.length>0?(0,r.jsx)("div",{className:"grid"===S?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:E.map(e=>(0,r.jsx)(c.default,{tool:e},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(h.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]}),(0,r.jsxs)("section",{className:"mt-16 bg-gray-50 rounded-lg p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"相关分类"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)(i(),{href:"/categories/image-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFA8"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"图像生成"})]}),(0,r.jsxs)(i(),{href:"/categories/code-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"代码生成"})]}),(0,r.jsxs)(i(),{href:"/categories/data-analysis",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"数据分析"})]}),(0,r.jsxs)(i(),{href:"/categories/audio-processing",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFB5"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"音频处理"})]})]})]})]})}):(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"分类未找到"}),(0,r.jsx)("p",{className:"text-gray-600",children:"请检查URL或返回分类列表"}),(0,r.jsx)(i(),{href:"/categories",className:"text-blue-600 hover:text-blue-700 mt-4 inline-block",children:"返回分类列表"})]})})})}},98402:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(60687);t(43210);var a=t(85814),l=t.n(a),i=t(5973);let n=({children:e})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,3876,6707,635],()=>t(15730));module.exports=r})();
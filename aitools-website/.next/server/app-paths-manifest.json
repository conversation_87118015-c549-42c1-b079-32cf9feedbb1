{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/tools/[id]/comments/route": "app/api/tools/[id]/comments/route.js", "/api/tools/[id]/like/route": "app/api/tools/[id]/like/route.js", "/api/tools/route": "app/api/tools/route.js", "/categories/[slug]/page": "app/categories/[slug]/page.js", "/categories/page": "app/categories/page.js", "/tools/[id]/page": "app/tools/[id]/page.js", "/tools/page": "app/tools/page.js"}
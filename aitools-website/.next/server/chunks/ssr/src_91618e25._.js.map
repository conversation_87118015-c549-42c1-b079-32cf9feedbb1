{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport default function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    const metrics: PerformanceMetrics = {};\n\n    // 监控 First Contentful Paint (FCP)\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n        if (fcpEntry) {\n          metrics.fcp = fcpEntry.startTime;\n          reportMetric('FCP', fcpEntry.startTime);\n        }\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n\n    // 监控 Largest Contentful Paint (LCP)\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        reportMetric('LCP', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n\n    // 监控 First Input Delay (FID)\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          reportMetric('FID', entry.processingStart - entry.startTime);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n\n    // 监控 Cumulative Layout Shift (CLS)\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        reportMetric('CLS', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n\n    // 监控 Time to First Byte (TTFB)\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        metrics.ttfb = ttfb;\n        reportMetric('TTFB', ttfb);\n      }\n    };\n\n    // 报告性能指标\n    const reportMetric = (name: string, value: number) => {\n      // 在开发环境中输出到控制台\n      if (process.env.NODE_ENV === 'development') {\n        console.log(`Performance Metric - ${name}:`, value);\n      }\n\n      // 在生产环境中可以发送到分析服务\n      // 例如 Google Analytics, Vercel Analytics 等\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', 'web_vitals', {\n          event_category: 'Performance',\n          event_label: name,\n          value: Math.round(value),\n          non_interaction: true,\n        });\n      }\n    };\n\n    // 检查浏览器支持\n    if (typeof PerformanceObserver !== 'undefined') {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n    }\n\n    observeTTFB();\n\n    // 页面卸载时报告最终指标\n    const reportFinalMetrics = () => {\n      if (Object.keys(metrics).length > 0) {\n        // 可以发送到分析服务\n        console.log('Final Performance Metrics:', metrics);\n      }\n    };\n\n    window.addEventListener('beforeunload', reportFinalMetrics);\n\n    return () => {\n      window.removeEventListener('beforeunload', reportFinalMetrics);\n    };\n  }, []);\n\n  return null; // 这是一个无UI的监控组件\n}\n\n// 性能优化建议\nexport const PerformanceOptimizations = {\n  // FCP 优化建议\n  fcp: {\n    good: 1800, // < 1.8s\n    needsImprovement: 3000, // 1.8s - 3s\n    suggestions: [\n      '减少服务器响应时间',\n      '消除阻塞渲染的资源',\n      '压缩CSS和JavaScript',\n      '使用CDN加速资源加载',\n    ],\n  },\n  \n  // LCP 优化建议\n  lcp: {\n    good: 2500, // < 2.5s\n    needsImprovement: 4000, // 2.5s - 4s\n    suggestions: [\n      '优化图片加载',\n      '预加载关键资源',\n      '减少JavaScript执行时间',\n      '使用服务端渲染',\n    ],\n  },\n  \n  // FID 优化建议\n  fid: {\n    good: 100, // < 100ms\n    needsImprovement: 300, // 100ms - 300ms\n    suggestions: [\n      '减少JavaScript执行时间',\n      '分割长任务',\n      '使用Web Workers',\n      '延迟加载非关键JavaScript',\n    ],\n  },\n  \n  // CLS 优化建议\n  cls: {\n    good: 0.1, // < 0.1\n    needsImprovement: 0.25, // 0.1 - 0.25\n    suggestions: [\n      '为图片和视频设置尺寸属性',\n      '避免在现有内容上方插入内容',\n      '使用transform动画而非改变布局的动画',\n      '预留广告位空间',\n    ],\n  },\n};\n\n// 声明全局gtag类型\ndeclare global {\n  interface Window {\n    gtag?: (...args: any[]) => void;\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAYe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;QAChB,wCAA2C;YACzC;QACF;;QAEA,MAAM;QAEN,kCAAkC;QAClC,MAAM;QAYN,oCAAoC;QACpC,MAAM;QAUN,6BAA6B;QAC7B,MAAM;QAWN,mCAAmC;QACnC,MAAM;QAeN,+BAA+B;QAC/B,MAAM;QASN,SAAS;QACT,MAAM;QA4BN,cAAc;QACd,MAAM;IAYR,GAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B;AAGO,MAAM,2BAA2B;IACtC,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean; // 新增：标识是否在liked页面\n}\n\nexport default function LikeButton({\n  toolId,\n  initialLikes = 0,\n  initialLiked = false,\n  onLoginRequired,\n  onUnlike,\n  isInLikedPage = false\n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const [liked, setLiked] = useState(initialLiked);\n  const [likes, setLikes] = useState(initialLikes);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // 获取点赞状态\n  useEffect(() => {\n    const fetchLikeStatus = async () => {\n      try {\n        const response = await fetch(`/api/tools/${toolId}/like`);\n        if (response.ok) {\n          const data = await response.json();\n          if (data.success) {\n            setLiked(data.data.liked);\n            setLikes(data.data.likes);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to fetch like status:', error);\n      }\n    };\n\n    if (session) {\n      fetchLikeStatus();\n    }\n  }, [toolId, session]);\n\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (isLoading) return;\n\n    setIsLoading(true);\n    \n    try {\n      // 如果在liked页面，发送强制unlike请求\n      const requestBody = isInLikedPage ? { forceUnlike: true } : {};\n\n      const response = await fetch(`/api/tools/${toolId}/like`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const newLikedState = data.data.liked;\n          setLiked(newLikedState);\n          setLikes(data.data.likes);\n\n          // 如果用户取消了点赞，并且提供了onUnlike回调，则调用它\n          if (!newLikedState && onUnlike) {\n            onUnlike(toolId);\n          }\n        }\n      } else {\n        const errorData = await response.json();\n        console.error('Like failed:', errorData.message);\n      }\n    } catch (error) {\n      console.error('Like request failed:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={isLoading}\n      className={`\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ${liked \n          ? 'bg-red-50 text-red-600 hover:bg-red-100' \n          : 'bg-gray-50 text-gray-600 hover:bg-gray-100'\n        }\n        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}\n        border border-gray-200 hover:border-gray-300\n      `}\n    >\n      {liked ? (\n        <FaHeart className=\"w-4 h-4 text-red-500\" />\n      ) : (\n        <FaRegHeart className=\"w-4 h-4\" />\n      )}\n      <span className=\"text-sm font-medium\">\n        {likes > 0 ? likes : ''}\n      </span>\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACf,QAAQ,EACR,gBAAgB,KAAK,EACL;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC;gBACxD,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,SAAS,KAAK,IAAI,CAAC,KAAK;wBACxB,SAAS,KAAK,IAAI,CAAC,KAAK;oBAC1B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,WAAW;QAEf,aAAa;QAEb,IAAI;YACF,0BAA0B;YAC1B,MAAM,cAAc,gBAAgB;gBAAE,aAAa;YAAK,IAAI,CAAC;YAE7D,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,EAAE;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,MAAM,gBAAgB,KAAK,IAAI,CAAC,KAAK;oBACrC,SAAS;oBACT,SAAS,KAAK,IAAI,CAAC,KAAK;oBAExB,iCAAiC;oBACjC,IAAI,CAAC,iBAAiB,UAAU;wBAC9B,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,gBAAgB,UAAU,OAAO;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,CAAC;;QAEV,EAAE,QACE,4CACA,6CACH;QACD,EAAE,YAAY,kCAAkC,kBAAkB;;MAEpE,CAAC;;YAEA,sBACC,8OAAC,8IAAA,CAAA,UAAO;gBAAC,WAAU;;;;;qCAEnB,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BAExB,8OAAC;gBAAK,WAAU;0BACb,QAAQ,IAAI,QAAQ;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  sizes?: string;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  fallbackSrc?: string;\n  onError?: () => void;\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  fill = false,\n  sizes,\n  placeholder = 'empty',\n  blurDataURL,\n  fallbackSrc = '/images/placeholder.svg',\n  onError,\n}: OptimizedImageProps) {\n  const [imgSrc, setImgSrc] = useState(src);\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n\n  const handleError = () => {\n    setHasError(true);\n    setIsLoading(false);\n    setImgSrc(fallbackSrc);\n    onError?.();\n  };\n\n  const handleLoad = () => {\n    setIsLoading(false);\n  };\n\n  // 生成模糊占位符\n  const generateBlurDataURL = (w: number = 10, h: number = 10) => {\n    const canvas = document.createElement('canvas');\n    canvas.width = w;\n    canvas.height = h;\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      ctx.fillStyle = '#f3f4f6';\n      ctx.fillRect(0, 0, w, h);\n    }\n    return canvas.toDataURL();\n  };\n\n  const imageProps = {\n    src: imgSrc,\n    alt,\n    className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,\n    onError: handleError,\n    onLoad: handleLoad,\n    priority,\n    placeholder: placeholder === 'blur' ? 'blur' as const : 'empty' as const,\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || (fill ? '100vw' : undefined),\n  };\n\n  if (fill) {\n    return (\n      <div className=\"relative overflow-hidden\">\n        <Image\n          {...imageProps}\n          fill\n          style={{ objectFit: 'cover' }}\n        />\n        {isLoading && (\n          <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      <Image\n        {...imageProps}\n        width={width}\n        height={height}\n      />\n      {isLoading && (\n        <div \n          className=\"absolute inset-0 bg-gray-200 animate-pulse\"\n          style={{ width, height }}\n        />\n      )}\n    </div>\n  );\n}\n\n// 预设的图片尺寸配置\nexport const ImageSizes = {\n  avatar: { width: 40, height: 40 },\n  avatarLarge: { width: 80, height: 80 },\n  toolLogo: { width: 64, height: 64 },\n  toolLogoLarge: { width: 128, height: 128 },\n  thumbnail: { width: 200, height: 150 },\n  card: { width: 300, height: 200 },\n  hero: { width: 1200, height: 600 },\n} as const;\n\n// 响应式图片尺寸字符串\nexport const ResponsiveSizes = {\n  avatar: '40px',\n  toolLogo: '64px',\n  thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  hero: '100vw',\n  full: '100vw',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAoBe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,cAAc,OAAO,EACrB,WAAW,EACX,cAAc,yBAAyB,EACvC,OAAO,EACa;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,YAAY;QACZ,aAAa;QACb,UAAU;QACV;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,UAAU;IACV,MAAM,sBAAsB,CAAC,IAAY,EAAE,EAAE,IAAY,EAAE;QACzD,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,KAAK;YACP,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG;QACxB;QACA,OAAO,OAAO,SAAS;IACzB;IAEA,MAAM,aAAa;QACjB,KAAK;QACL;QACA,WAAW,GAAG,UAAU,CAAC,EAAE,YAAY,cAAc,cAAc,gCAAgC,CAAC;QACpG,SAAS;QACT,QAAQ;QACR;QACA,aAAa,gBAAgB,SAAS,SAAkB;QACxD,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS,CAAC,OAAO,UAAU,SAAS;IAC7C;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6HAAA,CAAA,UAAK;oBACH,GAAG,UAAU;oBACd,IAAI;oBACJ,OAAO;wBAAE,WAAW;oBAAQ;;;;;;gBAE7B,2BACC,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6HAAA,CAAA,UAAK;gBACH,GAAG,UAAU;gBACd,OAAO;gBACP,QAAQ;;;;;;YAET,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE;oBAAO;gBAAO;;;;;;;;;;;;AAKjC;AAGO,MAAM,aAAa;IACxB,QAAQ;QAAE,OAAO;QAAI,QAAQ;IAAG;IAChC,aAAa;QAAE,OAAO;QAAI,QAAQ;IAAG;IACrC,UAAU;QAAE,OAAO;QAAI,QAAQ;IAAG;IAClC,eAAe;QAAE,OAAO;QAAK,QAAQ;IAAI;IACzC,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,MAAM;QAAE,OAAO;QAAK,QAAQ;IAAI;IAChC,MAAM;QAAE,OAAO;QAAM,QAAQ;IAAI;AACnC;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,UAAU;IACV,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Eye, Heart, ExternalLink } from 'lucide-react';\nimport LikeButton from './tools/LikeButton';\nimport OptimizedImage, { ImageSizes, ResponsiveSizes } from './ui/OptimizedImage';\nimport { getToolPricingColor, getToolPricingText } from '@/constants/pricing';\n\ninterface ToolCardProps {\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n    website: string;\n    logo?: string;\n    category: string;\n    tags: string[];\n    pricing: 'free' | 'freemium' | 'paid';\n    views: number;\n    likes: number;\n  };\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean; // 新增：标识是否在liked页面\n}\n\nconst ToolCard: React.FC<ToolCardProps> = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false }) => {\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            {tool.logo ? (\n              <OptimizedImage\n                src={tool.logo}\n                alt={`${tool.name} logo`}\n                width={ImageSizes.toolLogo.width}\n                height={ImageSizes.toolLogo.height}\n                className=\"rounded-lg object-cover\"\n                sizes={ResponsiveSizes.toolLogo}\n                placeholder=\"blur\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tool.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {tool.name}\n              </h3>\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getToolPricingColor(tool.pricing)}`}>\n                {getToolPricingText(tool.pricing)}\n              </span>\n            </div>\n          </div>\n          \n          <a\n            href={tool.website}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n          >\n            <ExternalLink className=\"h-5 w-5\" />\n          </a>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {tool.description}\n        </p>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {tool.tags.slice(0, 3).map((tag, index) => (\n            <span\n              key={index}\n              className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\"\n            >\n              {tag}\n            </span>\n          ))}\n          {tool.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\">\n              +{tool.tags.length - 3}\n            </span>\n          )}\n        </div>\n\n        {/* Stats and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"h-4 w-4\" />\n              <span>{tool.views}</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Heart className=\"h-4 w-4\" />\n              <span>{tool.likes}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <LikeButton\n              toolId={tool._id}\n              initialLikes={tool.likes}\n              onLoginRequired={onLoginRequired}\n              onUnlike={onUnlike}\n              isInLikedPage={isInLikedPage}\n            />\n            <Link\n              href={`/tools/${tool._id}`}\n              className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\"\n            >\n              查看详情\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AA2BA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,gBAAgB,KAAK,EAAE;IAEnG,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,iBACR,8OAAC,0IAAA,CAAA,UAAc;oCACb,KAAK,KAAK,IAAI;oCACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;oCACxB,OAAO,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK;oCAChC,QAAQ,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;oCAClC,WAAU;oCACV,OAAO,0IAAA,CAAA,kBAAe,CAAC,QAAQ;oCAC/B,aAAY;;;;;yDAGd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8CAItC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAW,CAAC,wEAAwE,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,OAAO,GAAG;sDAC5H,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,OAAO;;;;;;;;;;;;;;;;;;sCAKtC,8OAAC;4BACC,MAAM,KAAK,OAAO;4BAClB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAE,WAAU;8BACV,KAAK,WAAW;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;wBAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;4BAAK,WAAU;;gCAA8F;gCAC1G,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yIAAA,CAAA,UAAU;oCACT,QAAQ,KAAK,GAAG;oCAChB,cAAc,KAAK,KAAK;oCACxB,iBAAiB;oCACjB,UAAU;oCACV,eAAe;;;;;;8CAEjB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oCAC1B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport ToolCard from '@/components/ToolCard';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport { Tool } from '@/lib/api';\nimport { ArrowLeft, Filter, Grid, List, ChevronDown } from 'lucide-react';\n\n// 分类信息接口\ninterface CategoryInfo {\n  _id: string;\n  name: string;\n  slug: string;\n  description: string;\n  icon: string;\n  color: string;\n  toolCount: number;\n}\n\ninterface CategoryPageClientProps {\n  categoryInfo: CategoryInfo | null;\n  tools: Tool[];\n  error: string | null;\n}\n\nconst pricingOptions = [\n  { value: '', label: '所有价格' },\n  { value: 'free', label: '免费' },\n  { value: 'freemium', label: '免费增值' },\n  { value: 'paid', label: '付费' }\n];\n\nconst sortOptions = [\n  { value: 'popular', label: '最受欢迎' },\n  { value: 'newest', label: '最新添加' },\n  { value: 'name', label: '名称排序' },\n  { value: 'views', label: '浏览量' }\n];\n\nexport default function CategoryPageClient({ categoryInfo, tools, error }: CategoryPageClientProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedPricing, setSelectedPricing] = useState('');\n  const [sortBy, setSortBy] = useState('popular');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [showFilters, setShowFilters] = useState(false);\n\n  if (error) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <ErrorMessage message={error} />\n      </div>\n    );\n  }\n\n  if (!categoryInfo) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center py-12\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">分类未找到</h3>\n          <p className=\"text-gray-600\">请检查URL或返回分类列表</p>\n          <Link href=\"/categories\" className=\"text-blue-600 hover:text-blue-700 mt-4 inline-block\">\n            返回分类列表\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  // Filter and sort tools\n  const filteredTools = tools.filter(tool => {\n    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tool.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;\n    \n    return matchesSearch && matchesPricing;\n  });\n\n  const sortedTools = [...filteredTools].sort((a, b) => {\n    switch (sortBy) {\n      case 'popular':\n        return (b.likes || 0) - (a.likes || 0);\n      case 'views':\n        return (b.views || 0) - (a.views || 0);\n      case 'name':\n        return a.name.localeCompare(b.name);\n      case 'newest':\n        if (a.createdAt && b.createdAt) {\n          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n        }\n        return 0;\n      default:\n        return 0;\n    }\n  });\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Breadcrumb */}\n      <div className=\"flex items-center space-x-2 text-sm text-gray-500 mb-6\">\n        <Link href=\"/\" className=\"hover:text-blue-600\">首页</Link>\n        <span>/</span>\n        <Link href=\"/categories\" className=\"hover:text-blue-600\">分类</Link>\n        <span>/</span>\n        <span className=\"text-gray-900\">{categoryInfo.name}</span>\n      </div>\n\n      {/* Back Button */}\n      <div className=\"mb-6\">\n        <Link\n          href=\"/categories\"\n          className=\"inline-flex items-center text-blue-600 hover:text-blue-700\"\n        >\n          <ArrowLeft className=\"mr-2 h-4 w-4\" />\n          返回分类列表\n        </Link>\n      </div>\n\n      {/* Category Header */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8\">\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div\n            className=\"w-16 h-16 rounded-lg flex items-center justify-center text-3xl\"\n            style={{ backgroundColor: categoryInfo.color }}\n          >\n            <span className=\"text-white\">\n              {categoryInfo.icon}\n            </span>\n          </div>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n              {categoryInfo.name}\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              {categoryInfo.toolCount} 个工具\n            </p>\n          </div>\n        </div>\n        <p className=\"text-gray-600 leading-relaxed\">\n          {categoryInfo.description}\n        </p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n        {/* Search Bar */}\n        <div className=\"relative mb-4\">\n          <input\n            type=\"text\"\n            placeholder=\"在此分类中搜索工具...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          <Filter className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n        </div>\n\n        {/* Filter Toggle Button (Mobile) */}\n        <div className=\"md:hidden mb-4\">\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\"\n          >\n            <Filter className=\"mr-2 h-4 w-4\" />\n            筛选选项\n            <ChevronDown className={`ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`} />\n          </button>\n        </div>\n\n        {/* Filters */}\n        <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`}>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">价格</label>\n            <select\n              value={selectedPricing}\n              onChange={(e) => setSelectedPricing(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {pricingOptions.map(option => (\n                <option key={option.value} value={option.value}>\n                  {option.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">排序</label>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {sortOptions.map(option => (\n                <option key={option.value} value={option.value}>\n                  {option.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">视图</label>\n            <div className=\"flex rounded-lg border border-gray-300\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${\n                  viewMode === 'grid'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                <Grid className=\"h-4 w-4 mx-auto\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${\n                  viewMode === 'list'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                <List className=\"h-4 w-4 mx-auto\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Results */}\n      <div className=\"mb-6\">\n        <p className=\"text-gray-600\">\n          显示 {sortedTools.length} 个结果\n          {searchTerm && ` 搜索 \"${searchTerm}\"`}\n        </p>\n      </div>\n\n      {/* Tools Grid/List */}\n      {sortedTools.length > 0 ? (\n        <div className={viewMode === 'grid' \n          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'\n          : 'space-y-4'\n        }>\n          {sortedTools.map((tool) => (\n            <ToolCard key={tool._id} tool={tool} />\n          ))}\n        </div>\n      ) : (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-400 mb-4\">\n            <Filter className=\"h-12 w-12 mx-auto\" />\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">未找到匹配的工具</h3>\n          <p className=\"text-gray-600\">\n            尝试调整搜索条件或筛选选项\n          </p>\n        </div>\n      )}\n\n      {/* Related Categories */}\n      <section className=\"mt-16 bg-gray-50 rounded-lg p-8\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">相关分类</h2>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          <Link\n            href=\"/categories/image-generation\"\n            className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n          >\n            <span className=\"text-2xl\">🎨</span>\n            <span className=\"text-sm font-medium text-gray-900\">图像生成</span>\n          </Link>\n          <Link\n            href=\"/categories/code-generation\"\n            className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n          >\n            <span className=\"text-2xl\">💻</span>\n            <span className=\"text-sm font-medium text-gray-900\">代码生成</span>\n          </Link>\n          <Link\n            href=\"/categories/data-analysis\"\n            className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n          >\n            <span className=\"text-2xl\">📊</span>\n            <span className=\"text-sm font-medium text-gray-900\">数据分析</span>\n          </Link>\n          <Link\n            href=\"/categories/audio-processing\"\n            className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n          >\n            <span className=\"text-2xl\">🎵</span>\n            <span className=\"text-sm font-medium text-gray-900\">音频处理</span>\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AA0BA,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO;QAAQ,OAAO;IAAK;IAC7B;QAAE,OAAO;QAAY,OAAO;IAAO;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAK;CAC9B;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAU,OAAO;IAAO;IACjC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAS,OAAO;IAAM;CAChC;AAEc,SAAS,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAA2B;IAChG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;;;;;;IAG7B;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAc,WAAU;kCAAsD;;;;;;;;;;;;;;;;;IAMjG;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC5F,MAAM,iBAAiB,CAAC,mBAAmB,KAAK,OAAO,KAAK;QAE5D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,cAAc;WAAI;KAAc,CAAC,IAAI,CAAC,CAAC,GAAG;QAC9C,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;YACvC,KAAK;gBACH,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;YACvC,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,IAAI,EAAE,SAAS,IAAI,EAAE,SAAS,EAAE;oBAC9B,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACxE;gBACA,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCAAsB;;;;;;kCAC/C,8OAAC;kCAAK;;;;;;kCACN,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAc,WAAU;kCAAsB;;;;;;kCACzD,8OAAC;kCAAK;;;;;;kCACN,8OAAC;wBAAK,WAAU;kCAAiB,aAAa,IAAI;;;;;;;;;;;;0BAIpD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAM1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,aAAa,KAAK;gCAAC;0CAE7C,cAAA,8OAAC;oCAAK,WAAU;8CACb,aAAa,IAAI;;;;;;;;;;;0CAGtB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,aAAa,IAAI;;;;;;kDAEpB,8OAAC;wCAAE,WAAU;;4CACV,aAAa,SAAS;4CAAC;;;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAE,WAAU;kCACV,aAAa,WAAW;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;0CAEZ,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;kCAIpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;8CAEnC,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAW,CAAC,uBAAuB,EAAE,cAAc,eAAe,IAAI;;;;;;;;;;;;;;;;;kCAKvF,8OAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,cAAc,UAAU,kBAAkB;;0CACjG,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;kDAET,eAAe,GAAG,CAAC,CAAA,uBAClB,8OAAC;gDAA0B,OAAO,OAAO,KAAK;0DAC3C,OAAO,KAAK;+CADF,OAAO,KAAK;;;;;;;;;;;;;;;;0CAO/B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;kDAET,YAAY,GAAG,CAAC,CAAA,uBACf,8OAAC;gDAA0B,OAAO,OAAO,KAAK;0DAC3C,OAAO,KAAK;+CADF,OAAO,KAAK;;;;;;;;;;;;;;;;0CAO/B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,kDAAkD,EAC5D,aAAa,SACT,2BACA,2CACJ;0DAEF,cAAA,8OAAC,yMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,kDAAkD,EAC5D,aAAa,SACT,2BACA,2CACJ;0DAEF,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAgB;wBACvB,YAAY,MAAM;wBAAC;wBACtB,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;;;;;;;;;;;;YAKvC,YAAY,MAAM,GAAG,kBACpB,8OAAC;gBAAI,WAAW,aAAa,SACzB,yDACA;0BAED,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,8HAAA,CAAA,UAAQ;wBAAgB,MAAM;uBAAhB,KAAK,GAAG;;;;;;;;;qCAI3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAOjC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAW;;;;;;kDAC3B,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAW;;;;;;kDAC3B,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAW;;;;;;kDAC3B,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAW;;;;;;kDAC3B,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE", "debugId": null}}]}
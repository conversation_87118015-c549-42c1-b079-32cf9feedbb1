(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5093],{2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(5155);function r(e){let{size:s="md",className:t=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4478:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(5155);t(2115);var r=t(6874),l=t.n(r),c=t(365);let n=e=>{let{children:s}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(c.default,{}),(0,a.jsx)("main",{className:"flex-1",children:s}),(0,a.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,a.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4653:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},5731:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let a=t(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class r{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...s.headers},...s},r=await fetch(t,a),l=await r.json();if(!r.ok)throw Error(l.error||"HTTP error! status: ".concat(r.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/tools".concat(t?"?".concat(t):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/user/liked-tools".concat(t?"?".concat(t):""))}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/admin/tools".concat(t?"?".concat(t):""))}async approveTool(e,s){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=a){this.baseURL=e}}let l=new r},5968:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6932:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8331:(e,s,t)=>{"use strict";t.d(s,{Bi:()=>c,PZ:()=>i,ch:()=>r,vK:()=>n,xO:()=>l});let a=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],r=a.map(e=>({value:e.slug,label:e.name})),l=[{value:"",label:"所有分类"},...r],c=a.reduce((e,s)=>(e[s.slug]=s.name,e),{}),n=a.reduce((e,s)=>(e[s.slug]=s,e),{}),i=e=>n[e];a.map(e=>e.slug)},8651:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(5155),r=t(2115),l=t(6874),c=t.n(l),n=t(5695),i=t(4478),o=t(7797),d=t(2731),x=t(9783),m=t(5731),h=t(8331),u=t(7550),g=t(6932),p=t(6474),j=t(4653),b=t(5968);let y=[{value:"",label:"所有价格"},{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}],v=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function f(){let e=(0,n.useParams)(),[s,t]=(0,r.useState)(null),[l,f]=(0,r.useState)([]),[N,w]=(0,r.useState)(!0),[A,k]=(0,r.useState)(""),[C,S]=(0,r.useState)(""),[T,P]=(0,r.useState)(""),[q,E]=(0,r.useState)("popular"),[I,L]=(0,r.useState)("grid"),[D,B]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e.slug&&M(e.slug)},[e.slug]);let M=async e=>{try{w(!0),k("");let s=await m.u.getTools({category:e,status:"approved",limit:100});if(s.success&&s.data){f(s.data.tools);let a=O(e,s.data.tools.length);t(a)}else k("获取分类数据失败")}catch(e){console.error("获取分类数据失败:",e),k("获取分类数据失败")}finally{w(!1)}},O=(e,s)=>{let t=(0,h.PZ)(e);return t?{_id:e,slug:e,name:t.name,description:t.description,icon:t.icon,color:t.color,toolCount:s}:{_id:e,slug:e,name:e.replace("-"," ").replace(/\b\w/g,e=>e.toUpperCase()),description:"AI tools in the ".concat(e," category."),icon:"\uD83E\uDD16",color:"#6B7280",toolCount:s}},_=[...l.filter(e=>{let s=e.name.toLowerCase().includes(C.toLowerCase())||e.description.toLowerCase().includes(C.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(C.toLowerCase())),t=!T||e.pricing===T;return s&&t})].sort((e,s)=>{switch(q){case"popular":return(s.likes||0)-(e.likes||0);case"views":return(s.views||0)-(e.views||0);case"name":return e.name.localeCompare(s.name);case"newest":if(e.createdAt&&s.createdAt)return new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime();return 0;default:return 0}});return N?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(d.A,{size:"lg"})})}):A?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(x.A,{message:A})})}):s?(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,a.jsx)(c(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(c(),{href:"/categories",className:"hover:text-blue-600",children:"分类"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:s.name})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(c(),{href:"/categories",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"返回分类列表"]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-3xl",style:{backgroundColor:s.color},children:(0,a.jsx)("span",{className:"text-white",children:s.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:s.name}),(0,a.jsxs)("p",{className:"text-lg text-gray-600",children:[s.toolCount," 个工具"]})]})]}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:s.description})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"在此分类中搜索工具...",value:C,onChange:e=>S(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(g.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"md:hidden mb-4",children:(0,a.jsxs)("button",{onClick:()=>B(!D),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,a.jsx)(p.A,{className:"ml-2 h-4 w-4 transform ".concat(D?"rotate-180":"")})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 ".concat(D?"block":"hidden md:grid"),children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,a.jsx)("select",{value:T,onChange:e=>P(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:y.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,a.jsx)("select",{value:q,onChange:e=>E(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:v.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,a.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>L("grid"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ".concat("grid"===I?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,a.jsx)(j.A,{className:"h-4 w-4 mx-auto"})}),(0,a.jsx)("button",{onClick:()=>L("list"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ".concat("list"===I?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,a.jsx)(b.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["显示 ",_.length," 个结果",C&&' 搜索 "'.concat(C,'"')]})}),_.length>0?(0,a.jsx)("div",{className:"grid"===I?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:_.map(e=>(0,a.jsx)(o.default,{tool:e},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(g.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,a.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]}),(0,a.jsxs)("section",{className:"mt-16 bg-gray-50 rounded-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"相关分类"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(c(),{href:"/categories/image-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFA8"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"图像生成"})]}),(0,a.jsxs)(c(),{href:"/categories/code-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"代码生成"})]}),(0,a.jsxs)(c(),{href:"/categories/data-analysis",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"数据分析"})]}),(0,a.jsxs)(c(),{href:"/categories/audio-processing",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFB5"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"音频处理"})]})]})]})]})}):(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"分类未找到"}),(0,a.jsx)("p",{className:"text-gray-600",children:"请检查URL或返回分类列表"}),(0,a.jsx)(c(),{href:"/categories",className:"text-blue-600 hover:text-blue-700 mt-4 inline-block",children:"返回分类列表"})]})})})}},8864:(e,s,t)=>{Promise.resolve().then(t.bind(t,8651))},9783:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(5155),r=t(5339),l=t(4416);function c(e){let{message:s,onClose:t,className:c=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:s})}),t&&(0,a.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>s(8864)),_N_E=e.O()}]);
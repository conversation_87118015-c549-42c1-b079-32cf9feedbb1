(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2379],{4653:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5216:(e,s,t)=>{Promise.resolve().then(t.bind(t,7112))},7112:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var l=t(5155),i=t(2115),r=t(4478),c=t(6874),a=t.n(c);let n=e=>{let{category:s}=e;return(0,l.jsx)(a(),{href:"/categories/".concat(s.slug),children:(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:s.color||"#3B82F6"},children:(0,l.jsx)("span",{className:"text-white",children:s.icon||"\uD83D\uDD27"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:s.name}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:[s.toolCount," 个工具"]})]})]}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:s.description})]})})})};var o=t(2731),d=t(9783),x=t(5731),m=t(8331),h=t(4653),g=t(3109);function u(){let[e,s]=(0,i.useState)([]),[t,c]=(0,i.useState)(!0),[a,u]=(0,i.useState)("");(0,i.useEffect)(()=>{p()},[]);let p=async()=>{try{c(!0),u("");let e=await x.u.getCategories();if(e.success&&e.data){let t=e.data.categories.map(e=>{let s=m.vK[e.id]||{description:"优质AI工具集合",icon:"\uD83D\uDD27",color:"#6B7280"};return{_id:e.id,name:e.name,slug:e.id,description:s.description,icon:s.icon,color:s.color,toolCount:e.count}});s(t)}}catch(e){u("获取分类列表失败，请稍后重试"),console.error("Error fetching categories:",e)}finally{c(!1)}},j=e.sort((e,s)=>s.toolCount-e.toolCount).slice(0,6);return t?(0,l.jsx)(r.A,{children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,l.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,l.jsx)(o.A,{size:"lg"})})})}):a?(0,l.jsx)(r.A,{children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,l.jsx)(d.A,{message:a})})}):(0,l.jsx)(r.A,{children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)(h.A,{className:"inline-block mr-3 h-10 w-10 text-blue-600"}),"AI 工具分类"]}),(0,l.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"按功能分类浏览我们精选的 AI 工具集合。每个分类都包含经过验证的高质量工具，帮助您快速找到所需的解决方案。"})]}),(0,l.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length}),(0,l.jsx)("div",{className:"text-gray-700",children:"个分类"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.reduce((e,s)=>e+s.toolCount,0)}),(0,l.jsx)("div",{className:"text-gray-700",children:"个工具"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length>0?Math.round(e.reduce((e,s)=>e+s.toolCount,0)/e.length):0}),(0,l.jsx)("div",{className:"text-gray-700",children:"平均每分类工具数"})]})]})}),(0,l.jsxs)("section",{className:"mb-16",children:[(0,l.jsxs)("div",{className:"flex items-center mb-8",children:[(0,l.jsx)(g.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"热门分类"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:j.map(e=>(0,l.jsx)(n,{category:e},e._id))})]}),(0,l.jsxs)("section",{children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"所有分类"}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:[e.length," 个分类"]})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,l.jsx)(n,{category:e},e._id))})]}),(0,l.jsxs)("section",{className:"mt-16 bg-blue-600 rounded-lg p-8 text-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"没有找到您需要的分类？"}),(0,l.jsx)("p",{className:"text-blue-100 mb-6",children:"我们持续添加新的工具和分类。如果您有建议或想要提交新工具，请联系我们。"}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsx)("a",{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:"提交新工具"}),(0,l.jsx)("a",{href:"/contact",className:"inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors",children:"联系我们"})]})]})]})})}},8331:(e,s,t)=>{"use strict";t.d(s,{Bi:()=>c,PZ:()=>n,ch:()=>i,vK:()=>a,xO:()=>r});let l=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],i=l.map(e=>({value:e.slug,label:e.name})),r=[{value:"",label:"所有分类"},...i],c=l.reduce((e,s)=>(e[s.slug]=s.name,e),{}),a=l.reduce((e,s)=>(e[s.slug]=s,e),{}),n=e=>a[e];l.map(e=>e.slug)}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,6302,8441,1684,7358],()=>s(5216)),_N_E=e.O()}]);
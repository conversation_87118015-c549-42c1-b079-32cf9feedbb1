(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3554],{4416:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4653:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},4717:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.bind(a,365)),Promise.resolve().then(a.bind(a,5913))},5339:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5913:(e,s,a)=>{"use strict";a.d(s,{default:()=>b});var t=a(5155),l=a(2115),r=a(7797),c=a(9783),n=a(3467),i=a(8331),d=a(7924),o=a(6932),u=a(6474),m=a(4653),x=a(5968);let g=i.xO,h=n.v4,p=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function b(e){var s;let{initialTools:a,error:n}=e,[i]=(0,l.useState)(a),[b,v]=(0,l.useState)(""),[y,j]=(0,l.useState)(""),[f,N]=(0,l.useState)(""),[A,k]=(0,l.useState)("popular"),[w,C]=(0,l.useState)("grid"),[I,M]=(0,l.useState)(!1),D=[...i.filter(e=>{let s=e.name.toLowerCase().includes(b.toLowerCase())||e.description.toLowerCase().includes(b.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(b.toLowerCase())),a=!y||e.category===y,t=!f||e.pricing===f;return s&&a&&t})].sort((e,s)=>{switch(A){case"popular":return(s.likes||0)-(e.likes||0);case"views":return(s.views||0)-(e.views||0);case"name":return e.name.localeCompare(s.name);case"newest":if(e.launchDate&&s.launchDate)return new Date(s.launchDate).getTime()-new Date(e.launchDate).getTime();return new Date(s.createdAt||"").getTime()-new Date(e.createdAt||"").getTime();default:return 0}});return n?(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsx)(c.A,{message:n})}):(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"AI 工具目录"}),(0,t.jsxs)("p",{className:"text-lg text-gray-600",children:["发现 ",i.length," 个精选的 AI 工具，提升您的工作效率"]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,t.jsxs)("div",{className:"relative mb-4",children:[(0,t.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或标签...",value:b,onChange:e=>v(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,t.jsx)(d.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,t.jsx)("div",{className:"md:hidden mb-4",children:(0,t.jsxs)("button",{onClick:()=>M(!I),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,t.jsx)(u.A,{className:"ml-2 h-4 w-4 transform ".concat(I?"rotate-180":"")})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 ".concat(I?"block":"hidden md:grid"),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"分类"}),(0,t.jsx)("select",{value:y,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:g.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,t.jsx)("select",{value:f,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:h.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,t.jsx)("select",{value:A,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:p.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,t.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,t.jsx)("button",{onClick:()=>C("grid"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ".concat("grid"===w?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,t.jsx)(m.A,{className:"h-4 w-4 mx-auto"})}),(0,t.jsx)("button",{onClick:()=>C("list"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ".concat("list"===w?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,t.jsx)(x.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["显示 ",D.length," 个结果",b&&' 搜索 "'.concat(b,'"'),y&&' 在 "'.concat(null==(s=g.find(e=>e.value===y))?void 0:s.label,'"')]})}),D.length>0?(0,t.jsx)("div",{className:"grid"===w?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:D.map(e=>(0,t.jsx)(r.default,{tool:e},e._id))}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:(0,t.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,t.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]})]})}},5968:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6932:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7924:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8331:(e,s,a)=>{"use strict";a.d(s,{Bi:()=>c,PZ:()=>i,ch:()=>l,vK:()=>n,xO:()=>r});let t=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],l=t.map(e=>({value:e.slug,label:e.name})),r=[{value:"",label:"所有分类"},...l],c=t.reduce((e,s)=>(e[s.slug]=s.name,e),{}),n=t.reduce((e,s)=>(e[s.slug]=s,e),{}),i=e=>n[e];t.map(e=>e.slug)},9783:(e,s,a)=>{"use strict";a.d(s,{A:()=>c});var t=a(5155),l=a(5339),r=a(4416);function c(e){let{message:s,onClose:a,className:c=""}=e;return(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(l.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("p",{className:"text-red-800 text-sm",children:s})}),a&&(0,t.jsx)("button",{onClick:a,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,t.jsx)(r.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>s(4717)),_N_E=e.O()}]);
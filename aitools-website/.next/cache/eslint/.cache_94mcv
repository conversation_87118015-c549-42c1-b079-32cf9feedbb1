[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "82", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts": "83", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx": "84"}, {"size": 12560, "mtime": 1750914757080, "results": "85", "hashOfConfig": "86"}, {"size": 17221, "mtime": 1751039272116, "results": "87", "hashOfConfig": "86"}, {"size": 15702, "mtime": 1751039524674, "results": "88", "hashOfConfig": "86"}, {"size": 5321, "mtime": 1750906802986, "results": "89", "hashOfConfig": "86"}, {"size": 1926, "mtime": 1751014639961, "results": "90", "hashOfConfig": "86"}, {"size": 2073, "mtime": 1751038886395, "results": "91", "hashOfConfig": "86"}, {"size": 3121, "mtime": 1751014815428, "results": "92", "hashOfConfig": "86"}, {"size": 171, "mtime": 1750921851894, "results": "93", "hashOfConfig": "86"}, {"size": 3714, "mtime": 1750921931408, "results": "94", "hashOfConfig": "86"}, {"size": 4489, "mtime": 1750930430193, "results": "95", "hashOfConfig": "86"}, {"size": 2140, "mtime": 1751072700801, "results": "96", "hashOfConfig": "86"}, {"size": 4403, "mtime": 1750924179468, "results": "97", "hashOfConfig": "86"}, {"size": 5614, "mtime": 1750951694652, "results": "98", "hashOfConfig": "86"}, {"size": 7403, "mtime": 1751038610858, "results": "99", "hashOfConfig": "86"}, {"size": 5019, "mtime": 1751038932824, "results": "100", "hashOfConfig": "86"}, {"size": 5538, "mtime": 1750952541605, "results": "101", "hashOfConfig": "86"}, {"size": 2484, "mtime": 1750938669998, "results": "102", "hashOfConfig": "86"}, {"size": 13441, "mtime": 1751073151166, "results": "103", "hashOfConfig": "86"}, {"size": 13916, "mtime": 1751072868169, "results": "104", "hashOfConfig": "86"}, {"size": 2545, "mtime": 1751032793319, "results": "105", "hashOfConfig": "86"}, {"size": 12077, "mtime": 1751074417048, "results": "106", "hashOfConfig": "86"}, {"size": 7375, "mtime": 1751073200876, "results": "107", "hashOfConfig": "86"}, {"size": 10389, "mtime": 1750945315721, "results": "108", "hashOfConfig": "86"}, {"size": 21016, "mtime": 1751039634571, "results": "109", "hashOfConfig": "86"}, {"size": 21004, "mtime": 1750945519465, "results": "110", "hashOfConfig": "86"}, {"size": 15711, "mtime": 1751072831098, "results": "111", "hashOfConfig": "86"}, {"size": 4543, "mtime": 1750930937103, "results": "112", "hashOfConfig": "86"}, {"size": 5246, "mtime": 1751040539744, "results": "113", "hashOfConfig": "86"}, {"size": 1197, "mtime": 1751075908286, "results": "114", "hashOfConfig": "86"}, {"size": 1425, "mtime": 1750903550616, "results": "115", "hashOfConfig": "86"}, {"size": 845, "mtime": 1750908285683, "results": "116", "hashOfConfig": "86"}, {"size": 3162, "mtime": 1751023074001, "results": "117", "hashOfConfig": "86"}, {"size": 505, "mtime": 1750908273441, "results": "118", "hashOfConfig": "86"}, {"size": 863, "mtime": 1750908296528, "results": "119", "hashOfConfig": "86"}, {"size": 5768, "mtime": 1750942157899, "results": "120", "hashOfConfig": "86"}, {"size": 4494, "mtime": 1751023171715, "results": "121", "hashOfConfig": "86"}, {"size": 9109, "mtime": 1750930558601, "results": "122", "hashOfConfig": "86"}, {"size": 6661, "mtime": 1750945557905, "results": "123", "hashOfConfig": "86"}, {"size": 4438, "mtime": 1750923424688, "results": "124", "hashOfConfig": "86"}, {"size": 867, "mtime": 1750922283437, "results": "125", "hashOfConfig": "86"}, {"size": 362, "mtime": 1750922147686, "results": "126", "hashOfConfig": "86"}, {"size": 8935, "mtime": 1750924218629, "results": "127", "hashOfConfig": "86"}, {"size": 3198, "mtime": 1750951009317, "results": "128", "hashOfConfig": "86"}, {"size": 2449, "mtime": 1750942881883, "results": "129", "hashOfConfig": "86"}, {"size": 7116, "mtime": 1751038853366, "results": "130", "hashOfConfig": "86"}, {"size": 5059, "mtime": 1750930729612, "results": "131", "hashOfConfig": "86"}, {"size": 921, "mtime": 1750903252798, "results": "132", "hashOfConfig": "86"}, {"size": 5018, "mtime": 1751073079886, "results": "133", "hashOfConfig": "86"}, {"size": 1667, "mtime": 1750903308052, "results": "134", "hashOfConfig": "86"}, {"size": 2141, "mtime": 1750921803605, "results": "135", "hashOfConfig": "86"}, {"size": 4854, "mtime": 1751073025366, "results": "136", "hashOfConfig": "86"}, {"size": 3406, "mtime": 1750921782108, "results": "137", "hashOfConfig": "86"}, {"size": 720, "mtime": 1750903327281, "results": "138", "hashOfConfig": "86"}, {"size": 3866, "mtime": 1750984404444, "results": "139", "hashOfConfig": "86"}, {"size": 2238, "mtime": 1750995712168, "results": "140", "hashOfConfig": "86"}, {"size": 4057, "mtime": 1751018497440, "results": "141", "hashOfConfig": "86"}, {"size": 5242, "mtime": 1751013821668, "results": "142", "hashOfConfig": "86"}, {"size": 1022, "mtime": 1750984456438, "results": "143", "hashOfConfig": "86"}, {"size": 11976, "mtime": 1751038689677, "results": "144", "hashOfConfig": "86"}, {"size": 2237, "mtime": 1750949131424, "results": "145", "hashOfConfig": "86"}, {"size": 3202, "mtime": 1750953105864, "results": "146", "hashOfConfig": "86"}, {"size": 8048, "mtime": 1751018710311, "results": "147", "hashOfConfig": "86"}, {"size": 6764, "mtime": 1751005731451, "results": "148", "hashOfConfig": "86"}, {"size": 4621, "mtime": 1751005744888, "results": "149", "hashOfConfig": "86"}, {"size": 12431, "mtime": 1751004268664, "results": "150", "hashOfConfig": "86"}, {"size": 3885, "mtime": 1751018851458, "results": "151", "hashOfConfig": "86"}, {"size": 7687, "mtime": 1751017905894, "results": "152", "hashOfConfig": "86"}, {"size": 3527, "mtime": 1751018284048, "results": "153", "hashOfConfig": "86"}, {"size": 3985, "mtime": 1751017840303, "results": "154", "hashOfConfig": "86"}, {"size": 3989, "mtime": 1750984256539, "results": "155", "hashOfConfig": "86"}, {"size": 22918, "mtime": 1751072978974, "results": "156", "hashOfConfig": "86"}, {"size": 3519, "mtime": 1751038552644, "results": "157", "hashOfConfig": "86"}, {"size": 4413, "mtime": 1751019030952, "results": "158", "hashOfConfig": "86"}, {"size": 2009, "mtime": 1751039551036, "results": "159", "hashOfConfig": "86"}, {"size": 6972, "mtime": 1751018762448, "results": "160", "hashOfConfig": "86"}, {"size": 5275, "mtime": 1751023043127, "results": "161", "hashOfConfig": "86"}, {"size": 2605, "mtime": 1751019665417, "results": "162", "hashOfConfig": "86"}, {"size": 9806, "mtime": 1751039656928, "results": "163", "hashOfConfig": "86"}, {"size": 5433, "mtime": 1751023279983, "results": "164", "hashOfConfig": "86"}, {"size": 3048, "mtime": 1751023262678, "results": "165", "hashOfConfig": "86"}, {"size": 3274, "mtime": 1751039670741, "results": "166", "hashOfConfig": "86"}, {"size": 6456, "mtime": 1751075660742, "results": "167", "hashOfConfig": "86"}, {"size": 3849, "mtime": 1751072665492, "results": "168", "hashOfConfig": "86"}, {"size": 8364, "mtime": 1751075897950, "results": "169", "hashOfConfig": "86"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["422", "423", "424", "425", "426", "427", "428", "429", "430", "431"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["432", "433", "434", "435", "436", "437"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["438", "439", "440", "441", "442"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["443", "444"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["445", "446"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["447"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["448", "449"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["450", "451", "452"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["453"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", ["454"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["455", "456"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["457", "458"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["459", "460", "461"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["462"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["463", "464", "465", "466", "467", "468"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["469"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["470", "471", "472", "473"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["474", "475", "476", "477", "478", "479", "480", "481", "482", "483"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["484", "485", "486"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["487", "488"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["489", "490", "491", "492"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["493"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["494", "495", "496", "497"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["498"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["499"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["500"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["501", "502"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["503", "504", "505", "506"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx", ["507", "508", "509", "510", "511"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx", ["512", "513"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx", ["514", "515"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx", ["516", "517", "518"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx", ["519"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["520"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx", ["521"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["522", "523", "524"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["525", "526", "527", "528", "529"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["530"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["531", "532", "533"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["534"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx", [], [], {"ruleId": "535", "severity": 2, "message": "536", "line": 11, "column": 3, "nodeType": null, "messageId": "537", "endLine": 11, "endColumn": 8}, {"ruleId": "535", "severity": 2, "message": "538", "line": 16, "column": 3, "nodeType": null, "messageId": "537", "endLine": 16, "endColumn": 8}, {"ruleId": "535", "severity": 2, "message": "539", "line": 17, "column": 3, "nodeType": null, "messageId": "537", "endLine": 17, "endColumn": 11}, {"ruleId": "535", "severity": 2, "message": "540", "line": 20, "column": 3, "nodeType": null, "messageId": "537", "endLine": 20, "endColumn": 7}, {"ruleId": "535", "severity": 2, "message": "541", "line": 25, "column": 7, "nodeType": null, "messageId": "537", "endLine": 25, "endColumn": 21}, {"ruleId": "542", "severity": 1, "message": "543", "line": 48, "column": 6, "nodeType": "544", "endLine": 48, "endColumn": 17, "suggestions": "545"}, {"ruleId": "535", "severity": 2, "message": "546", "line": 62, "column": 14, "nodeType": null, "messageId": "537", "endLine": 62, "endColumn": 17}, {"ruleId": "535", "severity": 2, "message": "547", "line": 69, "column": 9, "nodeType": null, "messageId": "537", "endLine": 69, "endColumn": 19}, {"ruleId": "535", "severity": 2, "message": "548", "line": 78, "column": 9, "nodeType": null, "messageId": "537", "endLine": 78, "endColumn": 24}, {"ruleId": "535", "severity": 2, "message": "549", "line": 91, "column": 9, "nodeType": null, "messageId": "537", "endLine": 91, "endColumn": 27}, {"ruleId": "535", "severity": 2, "message": "550", "line": 17, "column": 3, "nodeType": null, "messageId": "537", "endLine": 17, "endColumn": 17}, {"ruleId": "542", "severity": 1, "message": "551", "line": 61, "column": 6, "nodeType": "544", "endLine": 61, "endColumn": 20, "suggestions": "552"}, {"ruleId": "535", "severity": 2, "message": "546", "line": 78, "column": 14, "nodeType": null, "messageId": "537", "endLine": 78, "endColumn": 17}, {"ruleId": "535", "severity": 2, "message": "546", "line": 111, "column": 14, "nodeType": null, "messageId": "537", "endLine": 111, "endColumn": 17}, {"ruleId": "535", "severity": 2, "message": "546", "line": 137, "column": 14, "nodeType": null, "messageId": "537", "endLine": 137, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "554", "line": 304, "column": 27, "nodeType": "555", "endLine": 308, "endColumn": 29}, {"ruleId": "535", "severity": 2, "message": "556", "line": 74, "column": 9, "nodeType": null, "messageId": "537", "endLine": 74, "endColumn": 15}, {"ruleId": "535", "severity": 2, "message": "557", "line": 88, "column": 14, "nodeType": null, "messageId": "537", "endLine": 88, "endColumn": 19}, {"ruleId": "535", "severity": 2, "message": "557", "line": 104, "column": 14, "nodeType": null, "messageId": "537", "endLine": 104, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "554", "line": 179, "column": 15, "nodeType": "555", "endLine": 183, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "554", "line": 273, "column": 19, "nodeType": "555", "endLine": 278, "endColumn": 21}, {"ruleId": "558", "severity": 2, "message": "559", "line": 19, "column": 18, "nodeType": "560", "messageId": "561", "endLine": 19, "endColumn": 21, "suggestions": "562"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 55, "column": 22, "nodeType": "560", "messageId": "561", "endLine": 55, "endColumn": 25, "suggestions": "563"}, {"ruleId": "535", "severity": 2, "message": "564", "line": 8, "column": 27, "nodeType": null, "messageId": "537", "endLine": 8, "endColumn": 34}, {"ruleId": "558", "severity": 2, "message": "559", "line": 96, "column": 23, "nodeType": "560", "messageId": "561", "endLine": 96, "endColumn": 26, "suggestions": "565"}, {"ruleId": "535", "severity": 2, "message": "566", "line": 1, "column": 10, "nodeType": null, "messageId": "537", "endLine": 1, "endColumn": 21}, {"ruleId": "558", "severity": 2, "message": "559", "line": 179, "column": 20, "nodeType": "560", "messageId": "561", "endLine": 179, "endColumn": 23, "suggestions": "567"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 217, "column": 70, "nodeType": "560", "messageId": "561", "endLine": 217, "endColumn": 73, "suggestions": "568"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 22, "column": 18, "nodeType": "560", "messageId": "561", "endLine": 22, "endColumn": 21, "suggestions": "569"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 66, "column": 22, "nodeType": "560", "messageId": "561", "endLine": 66, "endColumn": 25, "suggestions": "570"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 168, "column": 70, "nodeType": "560", "messageId": "561", "endLine": 168, "endColumn": 73, "suggestions": "571"}, {"ruleId": "535", "severity": 2, "message": "557", "line": 56, "column": 14, "nodeType": null, "messageId": "537", "endLine": 56, "endColumn": 19}, {"ruleId": "542", "severity": 1, "message": "572", "line": 55, "column": 6, "nodeType": "544", "endLine": 55, "endColumn": 19, "suggestions": "573"}, {"ruleId": "535", "severity": 2, "message": "541", "line": 23, "column": 7, "nodeType": null, "messageId": "537", "endLine": 23, "endColumn": 21}, {"ruleId": "535", "severity": 2, "message": "546", "line": 100, "column": 14, "nodeType": null, "messageId": "537", "endLine": 100, "endColumn": 17}, {"ruleId": "542", "severity": 1, "message": "574", "line": 46, "column": 6, "nodeType": "544", "endLine": 46, "endColumn": 49, "suggestions": "575"}, {"ruleId": "535", "severity": 2, "message": "546", "line": 61, "column": 14, "nodeType": null, "messageId": "537", "endLine": 61, "endColumn": 17}, {"ruleId": "535", "severity": 2, "message": "576", "line": 11, "column": 3, "nodeType": null, "messageId": "537", "endLine": 11, "endColumn": 7}, {"ruleId": "535", "severity": 2, "message": "577", "line": 18, "column": 3, "nodeType": null, "messageId": "537", "endLine": 18, "endColumn": 7}, {"ruleId": "553", "severity": 1, "message": "554", "line": 97, "column": 19, "nodeType": "555", "endLine": 101, "endColumn": 21}, {"ruleId": "535", "severity": 2, "message": "557", "line": 104, "column": 14, "nodeType": null, "messageId": "537", "endLine": 104, "endColumn": 19}, {"ruleId": "535", "severity": 2, "message": "578", "line": 12, "column": 3, "nodeType": null, "messageId": "537", "endLine": 12, "endColumn": 7}, {"ruleId": "535", "severity": 2, "message": "579", "line": 15, "column": 3, "nodeType": null, "messageId": "537", "endLine": 15, "endColumn": 6}, {"ruleId": "535", "severity": 2, "message": "546", "line": 94, "column": 14, "nodeType": null, "messageId": "537", "endLine": 94, "endColumn": 17}, {"ruleId": "535", "severity": 2, "message": "546", "line": 111, "column": 14, "nodeType": null, "messageId": "537", "endLine": 111, "endColumn": 17}, {"ruleId": "535", "severity": 2, "message": "546", "line": 128, "column": 14, "nodeType": null, "messageId": "537", "endLine": 128, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "554", "line": 197, "column": 23, "nodeType": "555", "endLine": 201, "endColumn": 25}, {"ruleId": "553", "severity": 1, "message": "554", "line": 289, "column": 21, "nodeType": "555", "endLine": 293, "endColumn": 23}, {"ruleId": "535", "severity": 2, "message": "557", "line": 27, "column": 14, "nodeType": null, "messageId": "537", "endLine": 27, "endColumn": 19}, {"ruleId": "535", "severity": 2, "message": "557", "line": 46, "column": 14, "nodeType": null, "messageId": "537", "endLine": 46, "endColumn": 19}, {"ruleId": "558", "severity": 2, "message": "559", "line": 63, "column": 42, "nodeType": "560", "messageId": "561", "endLine": 63, "endColumn": 45, "suggestions": "580"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 64, "column": 42, "nodeType": "560", "messageId": "561", "endLine": 64, "endColumn": 45, "suggestions": "581"}, {"ruleId": "535", "severity": 2, "message": "557", "line": 70, "column": 12, "nodeType": null, "messageId": "537", "endLine": 70, "endColumn": 17}, {"ruleId": "558", "severity": 2, "message": "559", "line": 87, "column": 42, "nodeType": "560", "messageId": "561", "endLine": 87, "endColumn": 45, "suggestions": "582"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 87, "column": 81, "nodeType": "560", "messageId": "561", "endLine": 87, "endColumn": 84, "suggestions": "583"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 87, "column": 118, "nodeType": "560", "messageId": "561", "endLine": 87, "endColumn": 121, "suggestions": "584"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 96, "column": 21, "nodeType": "560", "messageId": "561", "endLine": 96, "endColumn": 24, "suggestions": "585"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 97, "column": 29, "nodeType": "560", "messageId": "561", "endLine": 97, "endColumn": 32, "suggestions": "586"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 98, "column": 28, "nodeType": "560", "messageId": "561", "endLine": 98, "endColumn": 31, "suggestions": "587"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 99, "column": 27, "nodeType": "560", "messageId": "561", "endLine": 99, "endColumn": 30, "suggestions": "588"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 103, "column": 72, "nodeType": "560", "messageId": "561", "endLine": 103, "endColumn": 75, "suggestions": "589"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 150, "column": 60, "nodeType": "560", "messageId": "561", "endLine": 150, "endColumn": 63, "suggestions": "590"}, {"ruleId": "535", "severity": 2, "message": "557", "line": 44, "column": 14, "nodeType": null, "messageId": "537", "endLine": 44, "endColumn": 19}, {"ruleId": "535", "severity": 2, "message": "557", "line": 84, "column": 14, "nodeType": null, "messageId": "537", "endLine": 84, "endColumn": 19}, {"ruleId": "535", "severity": 2, "message": "557", "line": 111, "column": 14, "nodeType": null, "messageId": "537", "endLine": 111, "endColumn": 19}, {"ruleId": "553", "severity": 1, "message": "554", "line": 61, "column": 13, "nodeType": "555", "endLine": 65, "endColumn": 15}, {"ruleId": "553", "severity": 1, "message": "554", "line": 93, "column": 21, "nodeType": "555", "endLine": 97, "endColumn": 23}, {"ruleId": "535", "severity": 2, "message": "591", "line": 5, "column": 27, "nodeType": null, "messageId": "537", "endLine": 5, "endColumn": 34}, {"ruleId": "535", "severity": 2, "message": "592", "line": 5, "column": 36, "nodeType": null, "messageId": "537", "endLine": 5, "endColumn": 46}, {"ruleId": "542", "severity": 1, "message": "593", "line": 55, "column": 6, "nodeType": "544", "endLine": 55, "endColumn": 14, "suggestions": "594"}, {"ruleId": "553", "severity": 1, "message": "554", "line": 156, "column": 13, "nodeType": "555", "endLine": 160, "endColumn": 15}, {"ruleId": "558", "severity": 2, "message": "559", "line": 4, "column": 34, "nodeType": "560", "messageId": "561", "endLine": 4, "endColumn": 37, "suggestions": "595"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 65, "column": 60, "nodeType": "560", "messageId": "561", "endLine": 65, "endColumn": 63, "suggestions": "596"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 145, "column": 31, "nodeType": "560", "messageId": "561", "endLine": 145, "endColumn": 34, "suggestions": "597"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 151, "column": 26, "nodeType": "560", "messageId": "561", "endLine": 151, "endColumn": 29, "suggestions": "598"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 152, "column": 26, "nodeType": "560", "messageId": "561", "endLine": 152, "endColumn": 29, "suggestions": "599"}, {"ruleId": "535", "severity": 2, "message": "600", "line": 1, "column": 8, "nodeType": null, "messageId": "537", "endLine": 1, "endColumn": 16}, {"ruleId": "535", "severity": 2, "message": "601", "line": 27, "column": 13, "nodeType": null, "messageId": "537", "endLine": 27, "endColumn": 26}, {"ruleId": "535", "severity": 2, "message": "602", "line": 5, "column": 8, "nodeType": null, "messageId": "537", "endLine": 5, "endColumn": 12}, {"ruleId": "535", "severity": 2, "message": "603", "line": 89, "column": 11, "nodeType": null, "messageId": "537", "endLine": 89, "endColumn": 14}, {"ruleId": "558", "severity": 2, "message": "559", "line": 159, "column": 25, "nodeType": "560", "messageId": "561", "endLine": 159, "endColumn": 28, "suggestions": "604"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 38, "column": 18, "nodeType": "560", "messageId": "561", "endLine": 38, "endColumn": 21, "suggestions": "605"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 51, "column": 22, "nodeType": "560", "messageId": "561", "endLine": 51, "endColumn": 25, "suggestions": "606"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 88, "column": 52, "nodeType": "560", "messageId": "561", "endLine": 88, "endColumn": 55, "suggestions": "607"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 89, "column": 52, "nodeType": "560", "messageId": "561", "endLine": 89, "endColumn": 55, "suggestions": "608"}, {"ruleId": "535", "severity": 2, "message": "609", "line": 19, "column": 17, "nodeType": null, "messageId": "537", "endLine": 19, "endColumn": 24}, {"ruleId": "558", "severity": 2, "message": "559", "line": 20, "column": 38, "nodeType": "560", "messageId": "561", "endLine": 20, "endColumn": 41, "suggestions": "610"}, {"ruleId": "542", "severity": 1, "message": "611", "line": 36, "column": 6, "nodeType": "544", "endLine": 36, "endColumn": 23, "suggestions": "612"}, {"ruleId": "535", "severity": 2, "message": "546", "line": 62, "column": 14, "nodeType": null, "messageId": "537", "endLine": 62, "endColumn": 17}, {"ruleId": "535", "severity": 2, "message": "546", "line": 86, "column": 14, "nodeType": null, "messageId": "537", "endLine": 86, "endColumn": 17}, {"ruleId": "558", "severity": 2, "message": "559", "line": 16, "column": 36, "nodeType": "560", "messageId": "561", "endLine": 16, "endColumn": 39, "suggestions": "613"}, {"ruleId": "542", "severity": 1, "message": "614", "line": 32, "column": 6, "nodeType": "544", "endLine": 32, "endColumn": 22, "suggestions": "615"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 18, "column": 36, "nodeType": "560", "messageId": "561", "endLine": 18, "endColumn": 39, "suggestions": "616"}, {"ruleId": "542", "severity": 1, "message": "614", "line": 32, "column": 6, "nodeType": "544", "endLine": 32, "endColumn": 22, "suggestions": "617"}, {"ruleId": "535", "severity": 2, "message": "609", "line": 12, "column": 17, "nodeType": null, "messageId": "537", "endLine": 12, "endColumn": 24}, {"ruleId": "558", "severity": 2, "message": "559", "line": 13, "column": 36, "nodeType": "560", "messageId": "561", "endLine": 13, "endColumn": 39, "suggestions": "618"}, {"ruleId": "542", "severity": 1, "message": "614", "line": 28, "column": 6, "nodeType": "544", "endLine": 28, "endColumn": 22, "suggestions": "619"}, {"ruleId": "535", "severity": 2, "message": "557", "line": 81, "column": 14, "nodeType": null, "messageId": "537", "endLine": 81, "endColumn": 19}, {"ruleId": "535", "severity": 2, "message": "546", "line": 55, "column": 14, "nodeType": null, "messageId": "537", "endLine": 55, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "554", "line": 483, "column": 19, "nodeType": "555", "endLine": 487, "endColumn": 21}, {"ruleId": "558", "severity": 2, "message": "559", "line": 50, "column": 33, "nodeType": "560", "messageId": "561", "endLine": 50, "endColumn": 36, "suggestions": "620"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 63, "column": 33, "nodeType": "560", "messageId": "561", "endLine": 63, "endColumn": 36, "suggestions": "621"}, {"ruleId": "558", "severity": 2, "message": "559", "line": 185, "column": 22, "nodeType": "560", "messageId": "561", "endLine": 185, "endColumn": 25, "suggestions": "622"}, {"ruleId": "535", "severity": 2, "message": "623", "line": 25, "column": 16, "nodeType": null, "messageId": "537", "endLine": 25, "endColumn": 23}, {"ruleId": "542", "severity": 1, "message": "624", "line": 31, "column": 6, "nodeType": "544", "endLine": 31, "endColumn": 21, "suggestions": "625"}, {"ruleId": "535", "severity": 2, "message": "546", "line": 46, "column": 14, "nodeType": null, "messageId": "537", "endLine": 46, "endColumn": 17}, {"ruleId": "553", "severity": 1, "message": "554", "line": 61, "column": 19, "nodeType": "555", "endLine": 65, "endColumn": 21}, {"ruleId": "553", "severity": 1, "message": "554", "line": 195, "column": 27, "nodeType": "555", "endLine": 199, "endColumn": 29}, {"ruleId": "553", "severity": 1, "message": "554", "line": 180, "column": 7, "nodeType": "555", "endLine": 189, "endColumn": 9}, {"ruleId": "535", "severity": 2, "message": "626", "line": 37, "column": 10, "nodeType": null, "messageId": "537", "endLine": 37, "endColumn": 18}, {"ruleId": "627", "severity": 1, "message": "628", "line": 78, "column": 9, "nodeType": "555", "endLine": 82, "endColumn": 11}, {"ruleId": "627", "severity": 1, "message": "628", "line": 92, "column": 7, "nodeType": "555", "endLine": 96, "endColumn": 9}, {"ruleId": "558", "severity": 2, "message": "559", "line": 42, "column": 82, "nodeType": "560", "messageId": "561", "endLine": 42, "endColumn": 85, "suggestions": "629"}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["630"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["631"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["632", "633"], ["634", "635"], "'request' is defined but never used.", ["636", "637"], "'NextRequest' is defined but never used.", ["638", "639"], ["640", "641"], ["642", "643"], ["644", "645"], ["646", "647"], "React Hook useEffect has a missing dependency: 'fetchCategoryData'. Either include it or remove the dependency array.", ["648"], "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["649"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", ["650", "651"], ["652", "653"], ["654", "655"], ["656", "657"], ["658", "659"], ["660", "661"], ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], ["670", "671"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["672"], ["673", "674"], ["675", "676"], ["677", "678"], ["679", "680"], ["681", "682"], "'mongoose' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'now' is assigned a value but never used.", ["683", "684"], ["685", "686"], ["687", "688"], ["689", "690"], ["691", "692"], "'session' is assigned a value but never used.", ["693", "694"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["695"], ["696", "697"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["698"], ["699", "700"], ["701"], ["702", "703"], ["704"], ["705", "706"], ["707", "708"], ["709", "710"], "'setTool' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRelatedTools'. Either include it or remove the dependency array.", ["711"], "'hasError' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["712", "713"], {"desc": "714", "fix": "715"}, {"desc": "716", "fix": "717"}, {"messageId": "718", "fix": "719", "desc": "720"}, {"messageId": "721", "fix": "722", "desc": "723"}, {"messageId": "718", "fix": "724", "desc": "720"}, {"messageId": "721", "fix": "725", "desc": "723"}, {"messageId": "718", "fix": "726", "desc": "720"}, {"messageId": "721", "fix": "727", "desc": "723"}, {"messageId": "718", "fix": "728", "desc": "720"}, {"messageId": "721", "fix": "729", "desc": "723"}, {"messageId": "718", "fix": "730", "desc": "720"}, {"messageId": "721", "fix": "731", "desc": "723"}, {"messageId": "718", "fix": "732", "desc": "720"}, {"messageId": "721", "fix": "733", "desc": "723"}, {"messageId": "718", "fix": "734", "desc": "720"}, {"messageId": "721", "fix": "735", "desc": "723"}, {"messageId": "718", "fix": "736", "desc": "720"}, {"messageId": "721", "fix": "737", "desc": "723"}, {"desc": "738", "fix": "739"}, {"desc": "740", "fix": "741"}, {"messageId": "718", "fix": "742", "desc": "720"}, {"messageId": "721", "fix": "743", "desc": "723"}, {"messageId": "718", "fix": "744", "desc": "720"}, {"messageId": "721", "fix": "745", "desc": "723"}, {"messageId": "718", "fix": "746", "desc": "720"}, {"messageId": "721", "fix": "747", "desc": "723"}, {"messageId": "718", "fix": "748", "desc": "720"}, {"messageId": "721", "fix": "749", "desc": "723"}, {"messageId": "718", "fix": "750", "desc": "720"}, {"messageId": "721", "fix": "751", "desc": "723"}, {"messageId": "718", "fix": "752", "desc": "720"}, {"messageId": "721", "fix": "753", "desc": "723"}, {"messageId": "718", "fix": "754", "desc": "720"}, {"messageId": "721", "fix": "755", "desc": "723"}, {"messageId": "718", "fix": "756", "desc": "720"}, {"messageId": "721", "fix": "757", "desc": "723"}, {"messageId": "718", "fix": "758", "desc": "720"}, {"messageId": "721", "fix": "759", "desc": "723"}, {"messageId": "718", "fix": "760", "desc": "720"}, {"messageId": "721", "fix": "761", "desc": "723"}, {"messageId": "718", "fix": "762", "desc": "720"}, {"messageId": "721", "fix": "763", "desc": "723"}, {"desc": "764", "fix": "765"}, {"messageId": "718", "fix": "766", "desc": "720"}, {"messageId": "721", "fix": "767", "desc": "723"}, {"messageId": "718", "fix": "768", "desc": "720"}, {"messageId": "721", "fix": "769", "desc": "723"}, {"messageId": "718", "fix": "770", "desc": "720"}, {"messageId": "721", "fix": "771", "desc": "723"}, {"messageId": "718", "fix": "772", "desc": "720"}, {"messageId": "721", "fix": "773", "desc": "723"}, {"messageId": "718", "fix": "774", "desc": "720"}, {"messageId": "721", "fix": "775", "desc": "723"}, {"messageId": "718", "fix": "776", "desc": "720"}, {"messageId": "721", "fix": "777", "desc": "723"}, {"messageId": "718", "fix": "778", "desc": "720"}, {"messageId": "721", "fix": "779", "desc": "723"}, {"messageId": "718", "fix": "780", "desc": "720"}, {"messageId": "721", "fix": "781", "desc": "723"}, {"messageId": "718", "fix": "782", "desc": "720"}, {"messageId": "721", "fix": "783", "desc": "723"}, {"messageId": "718", "fix": "784", "desc": "720"}, {"messageId": "721", "fix": "785", "desc": "723"}, {"messageId": "718", "fix": "786", "desc": "720"}, {"messageId": "721", "fix": "787", "desc": "723"}, {"desc": "788", "fix": "789"}, {"messageId": "718", "fix": "790", "desc": "720"}, {"messageId": "721", "fix": "791", "desc": "723"}, {"desc": "792", "fix": "793"}, {"messageId": "718", "fix": "794", "desc": "720"}, {"messageId": "721", "fix": "795", "desc": "723"}, {"desc": "792", "fix": "796"}, {"messageId": "718", "fix": "797", "desc": "720"}, {"messageId": "721", "fix": "798", "desc": "723"}, {"desc": "792", "fix": "799"}, {"messageId": "718", "fix": "800", "desc": "720"}, {"messageId": "721", "fix": "801", "desc": "723"}, {"messageId": "718", "fix": "802", "desc": "720"}, {"messageId": "721", "fix": "803", "desc": "723"}, {"messageId": "718", "fix": "804", "desc": "720"}, {"messageId": "721", "fix": "805", "desc": "723"}, {"desc": "806", "fix": "807"}, {"messageId": "718", "fix": "808", "desc": "720"}, {"messageId": "721", "fix": "809", "desc": "723"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "810", "text": "811"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "812", "text": "813"}, "suggestUnknown", {"range": "814", "text": "815"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "816", "text": "817"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "818", "text": "815"}, {"range": "819", "text": "817"}, {"range": "820", "text": "815"}, {"range": "821", "text": "817"}, {"range": "822", "text": "815"}, {"range": "823", "text": "817"}, {"range": "824", "text": "815"}, {"range": "825", "text": "817"}, {"range": "826", "text": "815"}, {"range": "827", "text": "817"}, {"range": "828", "text": "815"}, {"range": "829", "text": "817"}, {"range": "830", "text": "815"}, {"range": "831", "text": "817"}, "Update the dependencies array to be: [fetchCategoryData, params.slug]", {"range": "832", "text": "833"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "834", "text": "835"}, {"range": "836", "text": "815"}, {"range": "837", "text": "817"}, {"range": "838", "text": "815"}, {"range": "839", "text": "817"}, {"range": "840", "text": "815"}, {"range": "841", "text": "817"}, {"range": "842", "text": "815"}, {"range": "843", "text": "817"}, {"range": "844", "text": "815"}, {"range": "845", "text": "817"}, {"range": "846", "text": "815"}, {"range": "847", "text": "817"}, {"range": "848", "text": "815"}, {"range": "849", "text": "817"}, {"range": "850", "text": "815"}, {"range": "851", "text": "817"}, {"range": "852", "text": "815"}, {"range": "853", "text": "817"}, {"range": "854", "text": "815"}, {"range": "855", "text": "817"}, {"range": "856", "text": "815"}, {"range": "857", "text": "817"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "858", "text": "859"}, {"range": "860", "text": "815"}, {"range": "861", "text": "817"}, {"range": "862", "text": "815"}, {"range": "863", "text": "817"}, {"range": "864", "text": "815"}, {"range": "865", "text": "817"}, {"range": "866", "text": "815"}, {"range": "867", "text": "817"}, {"range": "868", "text": "815"}, {"range": "869", "text": "817"}, {"range": "870", "text": "815"}, {"range": "871", "text": "817"}, {"range": "872", "text": "815"}, {"range": "873", "text": "817"}, {"range": "874", "text": "815"}, {"range": "875", "text": "817"}, {"range": "876", "text": "815"}, {"range": "877", "text": "817"}, {"range": "878", "text": "815"}, {"range": "879", "text": "817"}, {"range": "880", "text": "815"}, {"range": "881", "text": "817"}, "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "882", "text": "883"}, {"range": "884", "text": "815"}, {"range": "885", "text": "817"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "886", "text": "887"}, {"range": "888", "text": "815"}, {"range": "889", "text": "817"}, {"range": "890", "text": "887"}, {"range": "891", "text": "815"}, {"range": "892", "text": "817"}, {"range": "893", "text": "887"}, {"range": "894", "text": "815"}, {"range": "895", "text": "817"}, {"range": "896", "text": "815"}, {"range": "897", "text": "817"}, {"range": "898", "text": "815"}, {"range": "899", "text": "817"}, "Update the dependencies array to be: [fetchRelatedTools, tool.category]", {"range": "900", "text": "901"}, {"range": "902", "text": "815"}, {"range": "903", "text": "817"}, [1125, 1136], "[fetchStats, timeRange]", [1646, 1660], "[fetchTools, statusFilter]", [672, 675], "unknown", [672, 675], "never", [1551, 1554], [1551, 1554], [2591, 2594], [2591, 2594], [4541, 4544], [4541, 4544], [5532, 5535], [5532, 5535], [802, 805], [802, 805], [1909, 1912], [1909, 1912], [4431, 4434], [4431, 4434], [1711, 1724], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, params.slug]", [1329, 1372], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [2548, 2551], [2548, 2551], [2587, 2590], [2587, 2590], [2624, 2627], [2624, 2627], [2798, 2801], [2798, 2801], [2847, 2850], [2847, 2850], [2906, 2909], [2906, 2909], [2964, 2967], [2964, 2967], [3089, 3092], [3089, 3092], [4739, 4742], [4739, 4742], [1439, 1447], "[fetchComments, toolId]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4406, 4409], [4406, 4409], [4562, 4565], [4562, 4565], [4621, 4624], [4621, 4624], [4266, 4269], [4266, 4269], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [809, 812], [809, 812], [1213, 1230], "[status, orderId, router, fetchOrderInfo]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875], [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768], [908, 923], "[fetchRelatedTools, tool.category]", [1144, 1147], [1144, 1147]]